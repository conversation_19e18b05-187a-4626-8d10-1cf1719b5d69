// Test file to demonstrate character sanitization functionality
// This can be run in the browser console to test the sanitizeText function

// Copy the sanitizeText function from popup.tsx
const sanitizeText = (text) => {
  if (!text || typeof text !== 'string') return '';
  
  return text
    // Normalize unicode characters
    .normalize('NFKC')
    // Replace various problematic quote characters with standard ones
    .replace(/[\u2018\u2019]/g, "'") // Smart single quotes
    .replace(/[\u201C\u201D]/g, '"') // Smart double quotes
    .replace(/[\u2013\u2014]/g, '-') // En dash, Em dash
    .replace(/\u2026/g, '...') // Ellipsis
    // Replace various whitespace characters with standard space
    .replace(/[\u00A0\u1680\u2000-\u200A\u202F\u205F\u3000]/g, ' ')
    // Replace zero-width characters
    .replace(/[\u200B\u200C\u200D\uFEFF]/g, '')
    // Replace other problematic characters
    .replace(/[\u00AD]/g, '') // Soft hyphen
    .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, '') // Control characters
    // Trim and collapse multiple spaces
    .replace(/\s+/g, ' ')
    .trim();
};

// Test cases with problematic characters that might come from Amazon Kindle
const testCases = [
  {
    name: "Smart quotes",
    input: ""This is a quote with smart quotes"",
    expected: '"This is a quote with smart quotes"'
  },
  {
    name: "Smart single quotes",
    input: "'This is a quote with smart single quotes'",
    expected: "'This is a quote with smart single quotes'"
  },
  {
    name: "Em dash and En dash",
    input: "This is a sentence—with an em dash and – an en dash",
    expected: "This is a sentence-with an em dash and - an en dash"
  },
  {
    name: "Ellipsis",
    input: "This sentence has an ellipsis…",
    expected: "This sentence has an ellipsis..."
  },
  {
    name: "Non-breaking spaces",
    input: "This\u00A0has\u00A0non-breaking\u00A0spaces",
    expected: "This has non-breaking spaces"
  },
  {
    name: "Zero-width characters",
    input: "This\u200Bhas\u200Czero\u200Dwidth\uFEFFcharacters",
    expected: "Thishaszerowidthcharacters"
  },
  {
    name: "Soft hyphen",
    input: "This has a soft\u00ADhyphen",
    expected: "This has a softhyphen"
  },
  {
    name: "Control characters",
    input: "This\u0001has\u0002control\u0003characters",
    expected: "Thishascontrolcharacters"
  },
  {
    name: "Multiple spaces",
    input: "This   has    multiple     spaces",
    expected: "This has multiple spaces"
  },
  {
    name: "Mixed problematic characters",
    input: ""This is a complex example…" with—various\u00A0problematic\u200Bcharacters\u00AD",
    expected: '"This is a complex example..." with-various problematiccharacters'
  },
  {
    name: "Empty string",
    input: "",
    expected: ""
  },
  {
    name: "Null input",
    input: null,
    expected: ""
  },
  {
    name: "Undefined input",
    input: undefined,
    expected: ""
  }
];

// Function to run tests
function runCharacterSanitizationTests() {
  console.log("Running character sanitization tests...\n");
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = sanitizeText(testCase.input);
    const success = result === testCase.expected;
    
    if (success) {
      passed++;
      console.log(`✅ Test ${index + 1} (${testCase.name}): PASSED`);
    } else {
      failed++;
      console.log(`❌ Test ${index + 1} (${testCase.name}): FAILED`);
      console.log(`   Input: "${testCase.input}"`);
      console.log(`   Expected: "${testCase.expected}"`);
      console.log(`   Got: "${result}"`);
    }
  });
  
  console.log(`\nTest Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log("🎉 All tests passed! Character sanitization is working correctly.");
  } else {
    console.log("⚠️ Some tests failed. Please check the sanitizeText function.");
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { sanitizeText, runCharacterSanitizationTests };
}

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined') {
  console.log("Character sanitization test file loaded. Run runCharacterSanitizationTests() to test.");
}
