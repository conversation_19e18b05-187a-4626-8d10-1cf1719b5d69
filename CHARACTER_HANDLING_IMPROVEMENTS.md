# Character Handling Improvements

This document outlines the improvements made to handle strange characters in quotes and notes when processing data from Amazon Kindle to Unearthed.

## Problem

Sometimes quotes and notes contain weird characters that can cause issues during processing. These characters can come from:

1. **Amazon Kindle HTML parsing** - Special Unicode characters, smart quotes, em dashes, etc.
2. **Data transmission** - Character encoding issues when sending to Unearthed API
3. **CSV export** - Improper handling of special characters in exported files

## Solution

### 1. Text Sanitization Function

Added a comprehensive `sanitizeText()` function that handles:

- **Unicode normalization** using `NFKC` form
- **Smart quotes** (`"` `"` → `"`, `'` `'` → `'`)
- **Dashes** (em dash `—`, en dash `–` → `-`)
- **Ellipsis** (`…` → `...`)
- **Various whitespace characters** (non-breaking spaces, etc. → regular space)
- **Zero-width characters** (removed completely)
- **Soft hyphens** (removed)
- **Control characters** (removed)
- **Multiple spaces** (collapsed to single space)

### 2. Application Points

The sanitization is applied at key points in the data pipeline:

#### Input from Amazon (Text Extraction)
- `popup.tsx` - `parseSingleBook()` function
- `background/index.ts` - `parseSingleBook()` function
- Applied to: quote content, note content, color, location, book titles, authors

#### Output to Unearthed (API Transmission)
- `popup.tsx` - `bookUploadProcess()` function
- `background/index.ts` - `bookUploadProcess()` function
- Applied before JSON.stringify() and API calls

#### CSV Export
- Enhanced `convertBooksToCSV()` function
- Added UTF-8 BOM for proper encoding in Excel
- Applied sanitization to all CSV cell content

### 3. API Headers Enhancement

Updated all API calls with proper encoding headers:
```javascript
headers: {
  "Content-Type": "application/json; charset=utf-8",
  "Accept": "application/json",
  Authorization: `Bearer ${API_KEY}~~~${secret}`
}
```

### 4. Error Handling

Added JSON parsing error handling to catch character encoding issues:
```javascript
try {
  await response.json()
} catch (jsonError) {
  console.error(`JSON parsing error:`, jsonError)
  throw new Error(`JSON parsing error`)
}
```

## Files Modified

### Primary Files
- `popup.tsx` - Main popup interface with sanitization applied
- `background/index.ts` - Background script with sanitization applied

### Test Files
- `test-character-sanitization.js` - Test cases for the sanitization function

## Testing

Run the character sanitization tests:

1. Open browser console
2. Load `test-character-sanitization.js`
3. Run `runCharacterSanitizationTests()`

The tests cover various problematic characters including:
- Smart quotes and apostrophes
- Em dashes and en dashes
- Ellipsis characters
- Non-breaking spaces
- Zero-width characters
- Soft hyphens
- Control characters
- Multiple spaces
- Mixed problematic characters

## Benefits

1. **Prevents processing failures** caused by problematic characters
2. **Ensures consistent data** between Amazon and Unearthed
3. **Improves CSV export compatibility** with various applications
4. **Better error handling** for character encoding issues
5. **Maintains data integrity** while cleaning problematic characters

## Usage

The sanitization is applied automatically throughout the data pipeline. No manual intervention is required. The system will:

1. Clean text when extracting from Amazon Kindle HTML
2. Sanitize data before sending to Unearthed API
3. Ensure proper encoding in CSV exports
4. Log any character encoding errors for debugging

## Future Considerations

- Monitor logs for any new types of problematic characters
- Consider adding more specific character mappings if needed
- Potentially add user configuration for character handling preferences
